package board

import (
	"fmt"
)

// <PERSON> represents a stone on the board
type Stone int

const (
	Empty Stone = iota
	Black
	White
)

// String returns string representation of a stone
func (s Stone) String() string {
	switch s {
	case Empty:
		return "."
	case Black:
		return "●"
	case White:
		return "○"
	default:
		return "?"
	}
}

// Position represents a position on the board
type Position struct {
	Row, Col int
}

// Board represents the game board
type Board struct {
	Size  int
	Grid  [][]Stone
	Moves []Position // History of moves
}

// NewBoard creates a new board with the specified size
func NewBoard(size int) *Board {
	grid := make([][]Stone, size)
	for i := range grid {
		grid[i] = make([]Stone, size)
	}
	
	return &Board{
		Size:  size,
		Grid:  grid,
		Moves: make([]Position, 0),
	}
}

// IsValidPosition checks if a position is within board bounds
func (b *Board) IsValidPosition(row, col int) bool {
	return row >= 0 && row < b.Size && col >= 0 && col < b.Size
}

// IsEmpty checks if a position is empty
func (b *Board) IsEmpty(row, col int) bool {
	if !b.IsValidPosition(row, col) {
		return false
	}
	return b.Grid[row][col] == Empty
}

// GetStone returns the stone at the given position
func (b *Board) GetStone(row, col int) Stone {
	if !b.IsValidPosition(row, col) {
		return Empty
	}
	return b.Grid[row][col]
}

// PlaceStone places a stone at the given position
func (b *Board) PlaceStone(row, col int, stone Stone) bool {
	if !b.IsEmpty(row, col) {
		return false
	}
	
	b.Grid[row][col] = stone
	b.Moves = append(b.Moves, Position{Row: row, Col: col})
	return true
}

// RemoveStone removes a stone from the given position
func (b *Board) RemoveStone(row, col int) Stone {
	if !b.IsValidPosition(row, col) {
		return Empty
	}
	
	stone := b.Grid[row][col]
	b.Grid[row][col] = Empty
	return stone
}

// GetLastMove returns the last move made
func (b *Board) GetLastMove() *Position {
	if len(b.Moves) == 0 {
		return nil
	}
	return &b.Moves[len(b.Moves)-1]
}

// UndoLastMove removes the last move from the board
func (b *Board) UndoLastMove() bool {
	if len(b.Moves) == 0 {
		return false
	}
	
	lastMove := b.Moves[len(b.Moves)-1]
	b.Grid[lastMove.Row][lastMove.Col] = Empty
	b.Moves = b.Moves[:len(b.Moves)-1]
	return true
}

// Copy creates a deep copy of the board
func (b *Board) Copy() *Board {
	newBoard := NewBoard(b.Size)
	
	// Copy grid
	for i := 0; i < b.Size; i++ {
		for j := 0; j < b.Size; j++ {
			newBoard.Grid[i][j] = b.Grid[i][j]
		}
	}
	
	// Copy moves
	newBoard.Moves = make([]Position, len(b.Moves))
	copy(newBoard.Moves, b.Moves)
	
	return newBoard
}

// String returns a string representation of the board
func (b *Board) String() string {
	result := "  "
	
	// Column headers
	for i := 0; i < b.Size; i++ {
		result += fmt.Sprintf("%2d", i)
	}
	result += "\n"
	
	// Rows
	for i := 0; i < b.Size; i++ {
		result += fmt.Sprintf("%2d", i)
		for j := 0; j < b.Size; j++ {
			result += fmt.Sprintf(" %s", b.Grid[i][j].String())
		}
		result += "\n"
	}
	
	return result
}

// GetEmptyPositions returns all empty positions on the board
func (b *Board) GetEmptyPositions() []Position {
	var positions []Position
	for i := 0; i < b.Size; i++ {
		for j := 0; j < b.Size; j++ {
			if b.Grid[i][j] == Empty {
				positions = append(positions, Position{Row: i, Col: j})
			}
		}
	}
	return positions
}

// CountStones returns the count of each stone type
func (b *Board) CountStones() (black, white int) {
	for i := 0; i < b.Size; i++ {
		for j := 0; j < b.Size; j++ {
			switch b.Grid[i][j] {
			case Black:
				black++
			case White:
				white++
			}
		}
	}
	return black, white
}
