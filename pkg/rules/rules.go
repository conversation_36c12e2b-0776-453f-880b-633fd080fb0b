package rules

import (
	"gomoku/pkg/board"
)

// Direction represents a direction on the board
type Direction struct {
	DRow, DCol int
}

// All 8 directions for checking alignments
var Directions = []Direction{
	{-1, -1}, {-1, 0}, {-1, 1}, // Up-left, Up, Up-right
	{0, -1}, {0, 1}, // Left, Right
	{1, -1}, {1, 0}, {1, 1}, // Down-left, Down, Down-right
}

// GameRules defines the rules for the game
type GameRules struct {
	BoardSize         int
	WinLength         int  // Number of stones needed to win (5)
	CaptureEnabled    bool // Whether capture is enabled
	CaptureWinCount   int  // Number of captures needed to win (10)
	ForbidDoubleThree bool // Whether double-three is forbidden
}

// DefaultRules returns the default Gomoku rules
func DefaultRules() *GameRules {
	return &GameRules{
		BoardSize:         19,
		WinLength:         5,
		CaptureEnabled:    true,
		CaptureWinCount:   10,
		ForbidDoubleThree: true,
	}
}

// CheckWinByAlignment checks if there's a win by alignment
func (r *GameRules) CheckWinByAlignment(b *board.Board, stone board.Stone) bool {
	for row := 0; row < b.Size; row++ {
		for col := 0; col < b.Size; col++ {
			if b.GetStone(row, col) == stone {
				// Check all 4 main directions (horizontal, vertical, diagonal)
				for i := 0; i < 4; i++ {
					dir := Directions[i]
					if r.countInDirection(b, row, col, dir.DRow, dir.DCol, stone) >= r.WinLength {
						return true
					}
				}
			}
		}
	}
	return false
}

// countInDirection counts stones in a specific direction from a position
func (r *GameRules) countInDirection(b *board.Board, row, col, dRow, dCol int, stone board.Stone) int {
	count := 1 // Count the starting stone

	// Count in positive direction
	currentRow, currentCol := row+dRow, col+dCol
	for b.IsValidPosition(currentRow, currentCol) && b.GetStone(currentRow, currentCol) == stone {
		count++
		currentRow, currentCol = currentRow+dRow, currentCol+dCol
	}

	// Count in negative direction
	currentRow, currentCol = row-dRow, col-dCol
	for b.IsValidPosition(currentRow, currentCol) && b.GetStone(currentRow, currentCol) == stone {
		count++
		currentRow, currentCol = currentRow-dRow, currentCol-dCol
	}

	return count
}

// CheckCapture checks if placing a stone at the given position captures any opponent stones
func (r *GameRules) CheckCapture(b *board.Board, row, col int, stone board.Stone) []board.Position {
	if !r.CaptureEnabled {
		return nil
	}

	opponent := r.getOpponent(stone)
	var captured []board.Position

	// Check all 4 main directions for captures
	for i := 0; i < 4; i++ {
		dir := Directions[i]
		capturedInDir := r.checkCaptureInDirection(b, row, col, dir.DRow, dir.DCol, stone, opponent)
		captured = append(captured, capturedInDir...)
	}

	return captured
}

// checkCaptureInDirection checks for captures in a specific direction
func (r *GameRules) checkCaptureInDirection(b *board.Board, row, col, dRow, dCol int, stone, opponent board.Stone) []board.Position {
	// Look for pattern: stone - opponent - opponent - stone
	pos1Row, pos1Col := row+dRow, col+dCol
	pos2Row, pos2Col := row+2*dRow, col+2*dCol
	pos3Row, pos3Col := row+3*dRow, col+3*dCol

	// Check if positions are valid
	if !b.IsValidPosition(pos1Row, pos1Col) || !b.IsValidPosition(pos2Row, pos2Col) || !b.IsValidPosition(pos3Row, pos3Col) {
		return nil
	}

	// Check for capture pattern
	if b.GetStone(pos1Row, pos1Col) == opponent &&
		b.GetStone(pos2Row, pos2Col) == opponent &&
		b.GetStone(pos3Row, pos3Col) == stone {
		return []board.Position{
			{Row: pos1Row, Col: pos1Col},
			{Row: pos2Row, Col: pos2Col},
		}
	}

	return nil
}

// PerformCapture removes captured stones from the board
func (r *GameRules) PerformCapture(b *board.Board, captured []board.Position) {
	for _, pos := range captured {
		b.RemoveStone(pos.Row, pos.Col)
	}
}

// CheckDoubleThree checks if placing a stone creates a double-three (forbidden move)
func (r *GameRules) CheckDoubleThree(b *board.Board, row, col int, stone board.Stone) bool {
	if !r.ForbidDoubleThree {
		return false
	}

	// Temporarily place the stone
	originalStone := b.GetStone(row, col)
	b.Grid[row][col] = stone

	threeCount := 0

	// Check all 4 main directions for free threes
	for i := 0; i < 4; i++ {
		dir := Directions[i]
		if r.isFreeThree(b, row, col, dir.DRow, dir.DCol, stone) {
			threeCount++
		}
	}

	// Restore original state
	b.Grid[row][col] = originalStone

	return threeCount >= 2
}

// isFreeThree checks if there's a free three in a specific direction
func (r *GameRules) isFreeThree(b *board.Board, row, col, dRow, dCol int, stone board.Stone) bool {
	// A free three is a line of 3 stones with open ends that can become a winning 4
	count := r.countInDirection(b, row, col, dRow, dCol, stone)

	if count != 3 {
		return false
	}

	// Check if both ends are open (this is a simplified check)
	// In a real implementation, you'd need more sophisticated logic
	startRow, startCol := row-dRow, col-dCol
	endRow, endCol := row+dRow, col+dCol

	// Find the actual ends of the three-stone line
	for b.IsValidPosition(startRow, startCol) && b.GetStone(startRow, startCol) == stone {
		startRow, startCol = startRow-dRow, startCol-dCol
	}
	for b.IsValidPosition(endRow, endCol) && b.GetStone(endRow, endCol) == stone {
		endRow, endCol = endRow+dRow, endCol+dCol
	}

	// Check if both ends are empty (free)
	startFree := b.IsValidPosition(startRow, startCol) && b.GetStone(startRow, startCol) == board.Empty
	endFree := b.IsValidPosition(endRow, endCol) && b.GetStone(endRow, endCol) == board.Empty

	return startFree && endFree
}

// IsValidMove checks if a move is valid according to the rules
func (r *GameRules) IsValidMove(b *board.Board, row, col int, stone board.Stone) bool {
	// Check if position is empty
	if !b.IsEmpty(row, col) {
		return false
	}

	// Check for double-three
	if r.CheckDoubleThree(b, row, col, stone) {
		return false
	}

	return true
}

// getOpponent returns the opponent stone
func (r *GameRules) getOpponent(stone board.Stone) board.Stone {
	if stone == board.Black {
		return board.White
	}
	return board.Black
}
