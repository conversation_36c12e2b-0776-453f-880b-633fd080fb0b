package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"

	"gomoku/internal/game"
	"gomoku/internal/ui"
)

func main() {
	fmt.Println("Gomoku - Starting...")

	// Check for command line arguments
	if len(os.Args) > 1 && os.Args[1] == "--cli" {
		runCLIGame()
		return
	}

	// Start GUI game
	err := runGUIGame()
	if err != nil {
		fmt.Printf("Error running GUI game: %v\n", err)
		fmt.Println("Try using --cli for command line mode.")
		os.Exit(1)
	}
}

func runCLIGame() {
	fmt.Println("=== Gomoku CLI Mode ===")
	fmt.Println("Commands:")
	fmt.Println("  move <row> <col> - Make a move")
	fmt.Println("  show - Show the board")
	fmt.Println("  quit - Exit the game")
	fmt.Println()

	// Create a new game (Human vs Human for testing)
	g := game.NewGame(19, game.Human, game.Human)

	scanner := bufio.NewScanner(os.Stdin)

	// Show initial board
	fmt.Println(g.String())

	for !g.IsGameOver() {
		fmt.Printf("%s> ", g.GetGameStateString())

		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		parts := strings.Fields(input)

		if len(parts) == 0 {
			continue
		}

		switch parts[0] {
		case "move", "m":
			if len(parts) != 3 {
				fmt.Println("Usage: move <row> <col>")
				continue
			}

			row, err1 := strconv.Atoi(parts[1])
			col, err2 := strconv.Atoi(parts[2])

			if err1 != nil || err2 != nil {
				fmt.Println("Invalid coordinates. Please use numbers.")
				continue
			}

			err := g.MakeMove(row, col)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				continue
			}

			fmt.Printf("Move time: %v\n", g.GetMoveTime())
			fmt.Println(g.String())

		case "show", "s":
			fmt.Println(g.String())

		case "quit", "q":
			fmt.Println("Thanks for playing!")
			return

		default:
			fmt.Println("Unknown command. Type 'quit' to exit.")
		}
	}

	if g.IsGameOver() {
		fmt.Printf("\nGame Over! %s\n", g.GetGameStateString())
	}
}

func runGUIGame() error {
	fmt.Println("=== Gomoku GUI Mode ===")
	fmt.Println("Controls:")
	fmt.Println("  Left click - Place stone")
	fmt.Println("  R - Restart game")
	fmt.Println("  U - Undo last move")
	fmt.Println("  ESC - Exit game")
	fmt.Println()

	// Create and run the GUI
	gameUI, err := ui.NewGameUI("Gomoku", 800, 600, 19)
	if err != nil {
		return fmt.Errorf("failed to create game UI: %v", err)
	}

	return gameUI.Run()
}
