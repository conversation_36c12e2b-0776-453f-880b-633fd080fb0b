NAME = Gomoku
GO_FILES = $(shell find . -name "*.go" -type f)
BUILD_DIR = build

# Default target
all: $(NAME)

# Build the executable
$(NAME): $(GO_FILES)
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(NAME) .

# Clean build artifacts
clean:
	rm -rf $(BUILD_DIR)

# Full clean (including Go module cache)
fclean: clean
	go clean -modcache

# Rebuild everything
re: fclean all

# Install dependencies
deps:
	go mod tidy
	go mod download

# Run the program
run: $(NAME)
	./$(BUILD_DIR)/$(NAME)

# Test the program
test:
	go test ./...

# Format code
fmt:
	go fmt ./...

# Lint code
lint:
	go vet ./...

.PHONY: all clean fclean re deps run test fmt lint
