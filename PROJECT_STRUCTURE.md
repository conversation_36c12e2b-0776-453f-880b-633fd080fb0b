# Gomoku Project Structure

## Overview
This is a Gomoku game implementation in Go with SDL2 for graphics, featuring AI opponent using Minimax algorithm.

## Directory Structure

```
gomoku/
├── main.go                 # Entry point
├── Makefile               # Build configuration
├── go.mod                 # Go module definition
├── go.sum                 # Go module checksums
├── build/                 # Build artifacts
│   └── Gomoku            # Compiled executable
├── internal/              # Private application code
│   ├── game/             # Core game logic
│   ├── ui/               # User interface components
│   └── ai/               # AI implementation
├── pkg/                   # Public library code
│   ├── board/            # Board representation
│   └── rules/            # Game rules implementation
└── cmd/                   # Command line tools
    └── gomoku/           # Main application
```

## Key Components

### Game Logic (`internal/game/`)
- Game state management
- Turn handling
- Win condition checking
- Capture mechanics

### UI (`internal/ui/`)
- SDL2 rendering
- Menu system
- Input handling
- Game board display

### AI (`internal/ai/`)
- Minimax algorithm
- Alpha-beta pruning
- Heuristic evaluation
- Move generation

### Board (`pkg/board/`)
- Board representation
- Move validation
- Position utilities

### Rules (`pkg/rules/`)
- Gomoku rules implementation
- Capture detection
- Free-three validation
- Win condition logic

## Build Commands

- `make` or `make all` - Build the executable
- `make clean` - Remove build artifacts
- `make fclean` - Full clean including Go cache
- `make re` - Rebuild everything
- `make run` - Build and run
- `make test` - Run tests
- `make fmt` - Format code
- `make lint` - Lint code
- `make deps` - Install/update dependencies

## Features to Implement

1. **Core Game** (Priority 1)
   - 19x19 board
   - Stone placement
   - Win detection (5+ in a row)
   - Capture mechanics
   - Turn management

2. **UI** (Priority 2)
   - Menu system
   - Game board rendering
   - Mouse/keyboard input
   - Timer display

3. **AI** (Priority 3)
   - Minimax with alpha-beta pruning
   - Heuristic evaluation
   - Move suggestion
   - Performance optimization

4. **Advanced Features** (Priority 4)
   - Rule variants
   - Starting positions
   - Debugging interface
   - Settings persistence
